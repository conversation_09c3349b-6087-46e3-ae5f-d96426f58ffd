import { ApplicationCommandRegistries, RegisterBehavior, SapphireClient } from '@sapphire/framework';
import { container, getRootData } from '@sapphire/pieces';
import * as colorette from 'colorette';
import { ClientOptions } from 'discord.js';
import path from 'node:path';
import { inspect } from 'node:util';
import { EnvSchema } from '../utils/env';
import { Trengo } from './trengo';

export class Client extends SapphireClient {
	public constructor(options: ClientOptions) {
		super(options);

		this.stores.registerPath(path.join(getRootData().root, 'app'));

		EnvSchema.parse(process.env);

		container.trengo = new Trengo();

		container.tickets = new Map();

		inspect.defaultOptions.depth = 1;

		colorette.createColors({ useColor: true });

		ApplicationCommandRegistries.setDefaultGuildIds([process.env.DISCORD_GUILD_ID]);
		ApplicationCommandRegistries.setDefaultBehaviorWhenNotIdentical(RegisterBehavior.BulkOverwrite);
	}
}

declare module '@sapphire/framework' {
	interface Container {
		tickets: Map<string, string>;
	}
}
