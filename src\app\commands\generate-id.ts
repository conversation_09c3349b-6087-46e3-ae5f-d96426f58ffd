import { ApplyOptions } from '@sapphire/decorators';
import { Command } from '@sapphire/framework';
import { ChannelType, MessageFlags, PermissionFlagsBits, TextChannel } from 'discord.js';

@ApplyOptions<Command.Options>({
	name: 'generate-id',
	description: 'a command to generate trengo-id for discord conversation channels based',
	requiredUserPermissions: PermissionFlagsBits.Administrator
})
export class GenerateIdCommand extends Command {
	public override registerApplicationCommands(registry: Command.Registry) {
		registry.registerChatInputCommand((builder) =>
			builder //
				.setName(this.name)
				.setDescription(this.description)
				.addChannelOption((option) =>
					option
						.setName('conversation-channel')
						.setDescription('a conversation channel to generate id for')
						.addChannelTypes(ChannelType.GuildText)
						.setRequired(true)
				)
		);
	}

	public override async chatInputRun(interaction: Command.ChatInputCommandInteraction) {
		const channel = interaction.options.getChannel('conversation-channel') as TextChannel;

		await channel.setTopic(this.container.trengo.toIdentifier(channel.name));

		return interaction.reply({ content: 'id is generated!', flags: MessageFlags.Ephemeral });
	}
}
