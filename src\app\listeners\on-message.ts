import { ApplyOptions } from '@sapphire/decorators';
import { Events, Listener } from '@sapphire/framework';
import { ChannelType, Message } from 'discord.js';

@ApplyOptions<Listener.Options>({
	event: Events.MessageCreate
})
export class OnMessage extends Listener<typeof Events.MessageCreate> {
	public override async run(message: Message) {
		if (!message.author.bot && message.channel.type === ChannelType.GuildText && message.channel.topic?.startsWith('custom-')) {
			if (message.content) {
				await this.container.trengo.sendMessage({
					contact: { name: message.author.username, identifier: message.channel.topic! },
					content: message.content
				});
			}

			for (const attachment of message.attachments.values()) {
				await this.container.trengo.sendMessage({
					contact: { name: message.author.username, identifier: message.channel.topic! },
					content: attachment.url
				});
			}
		}
	}
}
