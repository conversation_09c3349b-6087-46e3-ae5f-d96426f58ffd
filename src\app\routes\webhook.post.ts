import { ApplyOptions } from '@sapphire/decorators';
import { HttpCodes, Route } from '@sapphire/plugin-api';
import { ChannelType, TextChannel } from 'discord.js';
import z from 'zod';

const WebhookSchema = z.object({
	message_id: z.string(),
	ticket_id: z.string(),
	message: z.string(),
	user_id: z.string(),
	user_name: z.string(),
	user_email: z.string(),
	contact_id: z.string(),
	contact_name: z.string(),
	contact_email: z.string().optional(),
	contact_identifier: z.string().optional(),
	channel_id: z.string()
});

@ApplyOptions<Route.Options>({ route: '/webhook', methods: ['POST'] })
export class WebhookRoute extends Route {
	public override async run(request: Route.Request, response: Route.Response): Promise<void> {
		let payload: z.infer<typeof WebhookSchema>;

		try {
			payload = (await request.readBodyJson()) as z.infer<typeof WebhookSchema>;
			this.container.logger.debug('recv /webhook (parsed body):', payload);
		} catch (error) {
			this.container.logger.error('recv /webhook (failed to parse body):', error);
			return response.error(HttpCodes.BadRequest);
		}

		this.container.logger.info('recv /webhook');

		if (payload.contact_identifier?.startsWith('custom-')) {
			const id = this.container.trengo.fromIdentifier(payload.contact_identifier);

			let channel: TextChannel | undefined;

			if (this.container.tickets.has(id)) {
				channel = this.container.client.channels.cache.get(this.container.tickets.get(id)!) as TextChannel;
			} else {
				channel = this.container.client.channels.cache.find(
					(channel) => channel.type === ChannelType.GuildText && channel.name === id
				) as TextChannel;

				this.container.tickets.set(id, channel.id);
			}

			const message = await this.container.trengo.fetchMessage(payload);

			if (message.status === 200) {
				await channel?.send({ content: message.body.message, files: message.body.attachments.map((attachment) => attachment.full_url) });
			} else {
				this.container.logger.error(JSON.stringify(message, null, 2));
			}
		}

		return response.ok(HttpCodes.Accepted);
	}
}
