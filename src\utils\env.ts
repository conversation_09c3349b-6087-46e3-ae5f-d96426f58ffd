import z from 'zod';

export const EnvSchema = z.object({
	NODE_ENV: z.enum(['development', 'production', 'test']),
	DISCORD_TOKEN: z.string(),
	DISCORD_GUILD_ID: z.string(),
	TRENGO_TOKEN: z.string(),
	TRENGO_CUSTOM_CHANNEL_TOKEN: z.string(),
	TRENGO_CUSTOM_CHANNEL_ID: z.string(),
	TRENGO_WEBHOOK_SECRET: z.string()
});

export type Env = z.infer<typeof EnvSchema>;

declare global {
	namespace NodeJS {
		interface ProcessEnv extends z.infer<typeof EnvSchema> {}
	}
}
