import { container } from '@sapphire/framework';
import { initClient, initContract } from '@ts-rest/core';
import z from 'zod';
import { base62 } from '../utils/base62';

const UnprocessableEntitySchema = z.object({
	message: z.string(),
	errors: z.record(z.string(), z.array(z.string()))
});

const contract = initContract().router({
	storeCustomChannelMessage: {
		summary: `store's a custom channel message`,
		method: 'POST',
		path: '/custom_channel_messages',
		body: z.object({
			channel: z.string(),
			contact: z.object({
				name: z.string(),
				email: z.string().optional(),
				identifier: z.string()
			}),
			body: z.object({
				text: z.string()
			}),
			attachments: z.array(z.object({ name: z.string(), url: z.string() })).optional()
		}),
		responses: {
			200: z.string(),
			422: UnprocessableEntitySchema
		}
	},
	fetchMessage: {
		summary: `fetches a message`,
		method: 'GET',
		path: '/tickets/:ticket_id/messages/:message_id',
		pathParams: z.object({
			ticket_id: z.string(),
			message_id: z.string()
		}),
		responses: {
			200: z.object({
				message: z.string(),
				attachments: z.array(z.object({ full_url: z.string(), mime_type: z.string() }))
			}),
			422: UnprocessableEntitySchema
		}
	}
});

export class Trengo {
	#client;
	#identifierPrefix = 'custom-';

	public constructor() {
		this.#client = initClient(contract, {
			baseUrl: 'https://app.trengo.com/api/v2',
			baseHeaders: {
				accept: 'application/json',
				'content-type': 'application/json',
				authorization: `Bearer ${process.env.TRENGO_TOKEN}`
			}
		});
	}

	public async sendMessage(args: { contact: { name: string; identifier: string }; content: string }) {
		const response = await this.#client.storeCustomChannelMessage({
			body: {
				channel: process.env.TRENGO_CUSTOM_CHANNEL_TOKEN,
				contact: args.contact,
				body: { text: args.content }
			}
		});

		if (response.status === 200) {
			container.logger.debug(JSON.stringify(response, null, 2));
		} else {
			container.logger.error(JSON.stringify(response, null, 2));
		}

		return response;
	}

	public async fetchMessage(args: { ticket_id: string; message_id: string }) {
		const response = await this.#client.fetchMessage({ params: args });

		if (response.status === 200) {
			container.logger.debug(JSON.stringify(response, null, 2));
		} else {
			container.logger.error(JSON.stringify(response, null, 2));
		}

		return response;
	}

	public toIdentifier(id: string): string {
		const encodedId = base62.encode(id);
		const needed = 20 - encodedId.length - this.#identifierPrefix.length;
		return `${this.#identifierPrefix}${encodedId}${needed > 0 ? '-'.repeat(needed) : ''}`;
	}

	public fromIdentifier(identifier: string): string {
		const encodedId = identifier.substring(this.#identifierPrefix.length).replace(/-+$/, '');
		return base62.decode(encodedId);
	}
}

declare module '@sapphire/framework' {
	interface Container {
		trengo: Trengo;
	}
}
